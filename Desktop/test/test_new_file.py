import pandas as pd

file_path = 'ai_models_with_timeline_20250726_230330.xlsx'
try:
    df = pd.read_excel(file_path)
    df = df.dropna(axis=1, how='all')
    print(f'✓ Successfully loaded: {file_path}')
    print(f'✓ Dataset shape: {df.shape}')
    print(f'✓ Columns: {list(df.columns[:5])}...')
    if 'timeline_year' in df.columns:
        print(f'✓ Year range: {df["timeline_year"].min()} - {df["timeline_year"].max()}')
    if 'creator_name' in df.columns:
        print(f'✓ Companies: {df["creator_name"].nunique()}')
except Exception as e:
    print(f'✗ Error loading {file_path}: {e}')
