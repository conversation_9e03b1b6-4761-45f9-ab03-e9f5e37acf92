#!/usr/bin/env python3
"""
测试Excel文件读取脚本
"""

import pandas as pd
import os

def test_excel_files():
    """测试读取Excel文件"""
    
    # 指定的文件名
    file_path = 'ai_models_with_timeline_20250726_230228.xlsx'
    
    print("当前目录文件:")
    for file in os.listdir('.'):
        if file.endswith('.xlsx'):
            print(f"  - {file}")
    
    print(f"\n尝试读取Excel文件: {file_path}")

    if os.path.exists(file_path):
        try:
            print(f"\n正在读取: {file_path}")
            df = pd.read_excel(file_path)

            print(f"✓ 成功读取文件: {file_path}")
            print(f"  数据形状: {df.shape}")
            print(f"  列数: {len(df.columns)}")
            print(f"  前几列: {list(df.columns[:5])}")

            # 检查关键列是否存在
            key_columns = ['model_name', 'creator_name', 'intelligence_index', 'math_index', 'coding_index']
            missing_cols = [col for col in key_columns if col not in df.columns]

            if missing_cols:
                print(f"  ⚠️ 缺少关键列: {missing_cols}")
            else:
                print(f"  ✓ 包含所有关键列")

            return df, file_path

        except Exception as e:
            print(f"  ✗ 读取失败: {e}")
    else:
        print(f"  ✗ 文件不存在: {file_path}")

    return None, None

if __name__ == "__main__":
    df, file_path = test_excel_files()
    
    if df is not None:
        print(f"\n=== 数据预览 ===")
        print(df.head())
        
        print(f"\n=== 数据类型 ===")
        print(df.dtypes)
    else:
        print("\n❌ 无法读取任何Excel文件")
