#!/usr/bin/env python3
"""
Test script to verify notebook fixes
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.preprocessing import StandardScaler
from sklearn.metrics import mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')

def test_basic_functionality():
    print("=== Testing Basic Functionality ===")
    
    # Load data
    file_path = 'dataset.xlsx'
    try:
        df = pd.read_excel(file_path)
        df = df.dropna(axis=1, how='all')
        print(f"✓ Data loaded successfully: {df.shape}")
    except Exception as e:
        print(f"✗ Error loading data: {e}")
        return False
    
    # Test company specialization analysis (the part that was causing errors)
    try:
        print("\n=== Testing Company Specialization Analysis ===")
        df_filtered = df[(df['timeline_year'] >= 2023) & (df['timeline_year'] <= 2025)].copy()
        
        # Calculate relative strengths for each company
        company_strengths = df_filtered.groupby('creator_name').agg({
            'intelligence_index': 'mean',
            'coding_index': 'mean',
            'math_index': 'mean'
        }).dropna()
        
        # Identify each company's strongest capability (FIXED VERSION)
        numeric_cols = ['intelligence_index', 'coding_index', 'math_index']
        company_strengths['strongest_capability'] = company_strengths[numeric_cols].idxmax(axis=1)
        company_strengths['strength_score'] = company_strengths[numeric_cols].max(axis=1)
        company_strengths['specialization_index'] = (
            company_strengths[numeric_cols].max(axis=1) - 
            company_strengths[numeric_cols].mean(axis=1)
        ) / company_strengths[numeric_cols].std(axis=1)
        
        print(f"✓ Company specialization analysis completed")
        print(f"✓ Analyzed {len(company_strengths)} companies")
        
        # Test visualization data preparation
        top_companies = company_strengths.nlargest(10, 'strength_score')
        print(f"✓ Top companies identified: {len(top_companies)}")
        
    except Exception as e:
        print(f"✗ Error in company specialization analysis: {e}")
        return False
    
    # Test regression analysis
    try:
        print("\n=== Testing Regression Analysis ===")
        df2 = df.dropna(subset=['math_index', 'coding_index'])
        if len(df2) > 10:
            X = df2[['math_index']].values.reshape(-1, 1)
            y = df2['coding_index'].values
            
            model = LinearRegression()
            model.fit(X, y)
            slope = model.coef_[0]
            intercept = model.intercept_
            r_sq = model.score(X, y)
            
            print(f"✓ Linear regression completed")
            print(f"✓ R² score: {r_sq:.3f}")
        else:
            print("⚠ Insufficient data for regression analysis")
            
    except Exception as e:
        print(f"✗ Error in regression analysis: {e}")
        return False
    
    # Test cost-performance analysis
    try:
        print("\n=== Testing Cost-Performance Analysis ===")
        cost_performance = df[['creator_name', 'intelligence_index', 'coding_index', 'math_index', 
                              'price_1m_input_tokens', 'price_1m_output_tokens']].dropna()
        
        if len(cost_performance) > 5:
            cost_performance['avg_cost'] = (cost_performance['price_1m_input_tokens'] + 
                                          cost_performance['price_1m_output_tokens']) / 2
            cost_performance['efficiency_score'] = (cost_performance['intelligence_index'] / 
                                                   (cost_performance['avg_cost'] + 0.001))
            
            print(f"✓ Cost-performance analysis completed")
            print(f"✓ Analyzed {len(cost_performance)} models with cost data")
        else:
            print("⚠ Insufficient cost data for analysis")
            
    except Exception as e:
        print(f"✗ Error in cost-performance analysis: {e}")
        return False
    
    print("\n=== All Tests Passed! ===")
    print("✓ The notebook should now run without errors")
    return True

if __name__ == "__main__":
    success = test_basic_functionality()
    if success:
        print("\n🎉 Notebook is ready to use!")
    else:
        print("\n❌ Some issues remain - check the errors above")
