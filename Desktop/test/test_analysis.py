#!/usr/bin/env python3
"""
Test script for AI model analysis functionality
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.metrics import r2_score
import warnings
warnings.filterwarnings('ignore')

def main():
    print("=== AI MODEL ANALYSIS TEST ===" )
    
    # Load data
    file_path = 'dataset.xlsx'
    try:
        df = pd.read_excel(file_path)
        df = df.dropna(axis=1, how='all')
        print(f"✓ Successfully loaded file: {file_path}")
        print(f"✓ Dataset shape: {df.shape}")
        print(f"✓ Year range: {df['timeline_year'].min()} - {df['timeline_year'].max()}")
        print(f"✓ Companies: {df['creator_name'].nunique()}")
    except Exception as e:
        print(f"✗ Error loading file: {e}")
        return
    
    # Test basic analysis
    print("\n=== BASIC ANALYSIS ===")
    key_metrics = ['intelligence_index', 'coding_index', 'math_index']
    available_metrics = [col for col in key_metrics if col in df.columns]
    print(f"Available metrics: {available_metrics}")
    
    if available_metrics:
        print("\nBasic statistics:")
        print(df[available_metrics].describe().round(2))
    
    # Test yearly analysis
    print("\n=== YEARLY ANALYSIS ===")
    df_filtered = df[(df['timeline_year'] >= 2023) & (df['timeline_year'] <= 2025)].copy()
    print(f"Filtered data (2023-2025): {len(df_filtered)} records")
    
    # Company performance over time
    yearly_performance = df_filtered.groupby(['creator_name', 'timeline_year']).agg({
        'intelligence_index': 'mean',
        'coding_index': 'mean', 
        'math_index': 'mean',
        'model_name': 'count'
    }).reset_index()
    
    companies_with_timeline = yearly_performance.groupby('creator_name')['timeline_year'].nunique()
    multi_year_companies = companies_with_timeline[companies_with_timeline >= 2].index
    print(f"Companies with multi-year data: {len(multi_year_companies)}")
    
    # Test trend analysis
    print("\n=== TREND ANALYSIS ===")
    
    def analyze_company_trends(company_name, df_data):
        """Simple trend analysis for a company"""
        company_data = df_data[df_data['creator_name'] == company_name].copy()
        
        if len(company_data) < 3:
            return None
        
        company_data = company_data.sort_values(['timeline_year', 'timeline_month'])
        company_data['time_index'] = (company_data['timeline_year'] - company_data['timeline_year'].min()) * 12 + company_data['timeline_month']
        
        results = {}
        for metric in ['intelligence_index', 'coding_index', 'math_index']:
            valid_data = company_data.dropna(subset=[metric, 'time_index'])
            
            if len(valid_data) >= 3:
                X = valid_data[['time_index']]
                y = valid_data[metric]
                
                model = LinearRegression()
                model.fit(X, y)
                
                slope = model.coef_[0]
                r2 = r2_score(y, model.predict(X))
                
                results[metric] = {
                    'slope': slope,
                    'r2': r2,
                    'improvement_rate': slope * 6  # 6-month improvement
                }
        
        return results
    
    # Analyze trends for top companies
    companies_with_data = df_filtered['creator_name'].value_counts()
    target_companies = companies_with_data[companies_with_data >= 3].index[:5]
    
    company_trends = {}
    for company in target_companies:
        trends = analyze_company_trends(company, df_filtered)
        if trends:
            company_trends[company] = trends
    
    print(f"Analyzed trends for {len(company_trends)} companies")
    
    # Generate predictions
    print("\n=== PREDICTIONS ===")
    for company, trends in company_trends.items():
        improvement_rates = {metric: data['improvement_rate'] for metric, data in trends.items()}
        strongest_metric = max(improvement_rates.keys(), key=lambda x: improvement_rates[x])
        avg_improvement = np.mean(list(improvement_rates.values()))
        
        print(f"\n{company}:")
        print(f"  Primary focus: {strongest_metric.replace('_', ' ').title()}")
        print(f"  Avg improvement rate: {avg_improvement:.2f} points/6 months")
    
    print("\n=== ANALYSIS COMPLETE ===")
    print("✓ All functionality tested successfully!")
    print("✓ Notebook should run without errors")

if __name__ == "__main__":
    main()
