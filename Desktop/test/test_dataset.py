import pandas as pd

file_path = 'dataset.xlsx'
try:
    df = pd.read_excel(file_path)
    df = df.dropna(axis=1, how='all')
    print(f'✓ Successfully loaded: {file_path}')
    print(f'✓ Dataset shape: {df.shape}')
    print(f'✓ Columns: {list(df.columns)}')
    
    # Check for key columns needed for analysis
    key_columns = ['model_name', 'creator_name', 'intelligence_index', 'coding_index', 'math_index', 'timeline_year', 'timeline_month']
    available_key_columns = [col for col in key_columns if col in df.columns]
    print(f'✓ Available key columns: {available_key_columns}')
    
    if 'timeline_year' in df.columns:
        print(f'✓ Year range: {df["timeline_year"].min()} - {df["timeline_year"].max()}')
    if 'creator_name' in df.columns:
        print(f'✓ Companies: {df["creator_name"].nunique()}')
        
    print('\n✓ Dataset is ready for analysis!')
    
except Exception as e:
    print(f'✗ Error loading {file_path}: {e}')
