# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split, cross_val_score
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler, LabelEncoder
import warnings
warnings.filterwarnings('ignore')

# Set plotting style
plt.style.use('default')
sns.set_palette('husl')
plt.rcParams['figure.figsize'] = (12, 8)

print("Libraries imported successfully!")

# Load the dataset with error handling
try:
    file_path = 'dataset.xlsx'
    df = pd.read_excel(file_path)
    print(f"Dataset loaded successfully!")
    print(f"Shape: {df.shape}")
    print(f"Columns: {list(df.columns)}")
    
    # Remove completely empty columns
    df = df.dropna(axis=1, how='all')
    
    # Display basic info
    print(f"\nAfter removing empty columns: {df.shape}")
    
except Exception as e:
    print(f"Error loading dataset: {e}")
    print("Please ensure 'dataset.xlsx' is in the current directory.")

# Display first few rows and basic statistics
print("=== DATASET OVERVIEW ===")
print(f"Total models: {len(df)}")
print(f"Total companies: {df['creator_name'].nunique()}")
print(f"Timeline range: {df['timeline_year'].min():.0f} - {df['timeline_year'].max():.0f}")

# Show sample data
print("\n=== SAMPLE DATA ===")
display(df.head())

# Key performance metrics summary
performance_cols = ['intelligence_index', 'coding_index', 'math_index']
print("\n=== PERFORMANCE METRICS SUMMARY ===")
display(df[performance_cols].describe())









# Data preparation for hypothesis testing
print("=== DATA PREPARATION FOR HYPOTHESIS TESTING ===")

# Create clean dataset for analysis
df_clean = df.copy()

# Add timeline decimal for easier analysis
df_clean['timeline_decimal'] = df_clean['timeline_year'] + (df_clean['timeline_month'] - 1) / 12

# Filter out rows with missing key performance metrics
performance_metrics = ['intelligence_index', 'coding_index', 'math_index']
for metric in performance_metrics:
    valid_count = df_clean[metric].notna().sum()
    print(f"{metric}: {valid_count} valid values out of {len(df_clean)}")

# Create company groups for analysis
major_companies = df_clean['creator_name'].value_counts().head(10).index.tolist()
print(f"\nMajor companies (top 10): {major_companies}")

# Create time periods for analysis
df_clean['time_period'] = df_clean['timeline_year'].apply(
    lambda x: '2023' if x <= 2023 else '2024-2025' if x >= 2024 else 'Unknown'
)

print(f"\nTime period distribution:")
print(df_clean['time_period'].value_counts())

# Hypothesis 1: Company Performance Differences
print("=== HYPOTHESIS 1: Company Performance Differences ===")

# Filter data for major companies with sufficient data
company_data = df_clean[df_clean['creator_name'].isin(major_companies[:5])].copy()
company_data = company_data.dropna(subset=['intelligence_index'])

print(f"Companies analyzed: {company_data['creator_name'].unique()}")
print(f"Total models: {len(company_data)}")

# Descriptive statistics by company
company_stats = company_data.groupby('creator_name')['intelligence_index'].agg([
    'count', 'mean', 'std', 'min', 'max'
]).round(2)

print("\n=== DESCRIPTIVE STATISTICS BY COMPANY ===")
display(company_stats)

# ANOVA test
company_groups = [group['intelligence_index'].values for name, group in company_data.groupby('creator_name')]
f_stat, p_value_anova = stats.f_oneway(*company_groups)

print(f"\n=== ANOVA RESULTS ===")
print(f"F-statistic: {f_stat:.4f}")
print(f"P-value: {p_value_anova:.4f}")
print(f"Significance level (α): 0.05")

if p_value_anova < 0.05:
    print("✅ CONCLUSION: Reject null hypothesis")
    print("Significant differences found between companies.")
else:
    print("❌ CONCLUSION: Fail to reject null hypothesis")
    print("No significant differences found between companies.")

# Effect size (eta-squared)
ss_between = sum([len(group) * (group.mean() - company_data['intelligence_index'].mean())**2 
                  for group in company_groups])
ss_total = sum([(x - company_data['intelligence_index'].mean())**2 
                for x in company_data['intelligence_index']])
eta_squared = ss_between / ss_total
print(f"\nEffect size (η²): {eta_squared:.4f}")

if eta_squared < 0.01:
    effect_size = "small"
elif eta_squared < 0.06:
    effect_size = "medium"
else:
    effect_size = "large"
print(f"Effect size interpretation: {effect_size}")

# Visualization for Hypothesis 1
plt.figure(figsize=(15, 10))

# Box plot comparing companies
plt.subplot(2, 2, 1)
sns.boxplot(data=company_data, x='creator_name', y='intelligence_index')
plt.title('Intelligence Index Distribution by Company')
plt.xticks(rotation=45)
plt.ylabel('Intelligence Index')

# Violin plot for distribution shape
plt.subplot(2, 2, 2)
sns.violinplot(data=company_data, x='creator_name', y='intelligence_index')
plt.title('Intelligence Index Distribution Shape by Company')
plt.xticks(rotation=45)
plt.ylabel('Intelligence Index')

# Mean comparison
plt.subplot(2, 2, 3)
company_means = company_data.groupby('creator_name')['intelligence_index'].mean().sort_values(ascending=False)
company_means.plot(kind='bar')
plt.title('Average Intelligence Index by Company')
plt.xticks(rotation=45)
plt.ylabel('Mean Intelligence Index')

# Sample sizes
plt.subplot(2, 2, 4)
company_counts = company_data['creator_name'].value_counts()
company_counts.plot(kind='bar')
plt.title('Number of Models by Company')
plt.xticks(rotation=45)
plt.ylabel('Number of Models')

plt.tight_layout()
plt.show()

print("\n=== HYPOTHESIS 1 SUMMARY ===")
print(f"ANOVA F-statistic: {f_stat:.4f}")
print(f"P-value: {p_value_anova:.4f}")
print(f"Effect size (η²): {eta_squared:.4f} ({effect_size})")
if p_value_anova < 0.05:
    print("✅ Significant differences found between companies")
else:
    print("❌ No significant differences found between companies")

pivot_math = df.pivot_table(
    index='creator_name',
    values='math_index',
    aggfunc='mean'
)
pivot_math.head(10)

numeric_cols = df.select_dtypes(include='number').columns.tolist()
pivot_input = df[['creator_name'] + numeric_cols]

# 5. 生成 pivot table：行是 creator, 列是各数值指标的平均值
pivot = pivot_input.pivot_table(
    index='creator_name',
    aggfunc='mean'
)

# 6. 打印或查看结果
print(pivot.head(20)) 

top10_math = (
    df.sort_values('math_index', ascending=False)
      .head(10)[['model_name', 'creator_name', 'math_index']]
)
top10_coding = (
    df.sort_values('coding_index', ascending=False)
      .head(10)[['model_name', 'creator_name', 'coding_index']]
)
top10_intel = (
    df.sort_values('intelligence_index', ascending=False)
      .head(10)[['model_name', 'creator_name', 'intelligence_index']]
)

# Hypothesis 1: OpenAI vs Google Math Performance
print("=== HYPOTHESIS 1: OpenAI vs Google Math Performance ===")

# Filter data for OpenAI and Google models with math scores
openai_math = df_clean[df_clean['creator_name'] == 'OpenAI']['math_index'].dropna()
google_math = df_clean[df_clean['creator_name'] == 'Google']['math_index'].dropna()

print(f"OpenAI models with math scores: {len(openai_math)}")
print(f"Google models with math scores: {len(google_math)}")

if len(openai_math) > 0 and len(google_math) > 0:
    # Calculate descriptive statistics
    openai_mean = openai_math.mean()
    google_mean = google_math.mean()
    observed_diff = openai_mean - google_mean
    
    print(f"\nDescriptive Statistics:")
    print(f"OpenAI Math Index - Mean: {openai_mean:.2f}, Std: {openai_math.std():.2f}")
    print(f"Google Math Index - Mean: {google_mean:.2f}, Std: {google_math.std():.2f}")
    print(f"Observed Difference: {observed_diff:.2f}")
    
    # Perform permutation test
    all_scores = pd.concat([openai_math, google_math])
    num_permutations = 10000
    simulated_diffs = []
    
    np.random.seed(42)  # For reproducibility
    for i in range(num_permutations):
        shuffled_scores = np.random.permutation(all_scores)
        group_a = shuffled_scores[:len(openai_math)]
        group_b = shuffled_scores[len(openai_math):]
        sim_diff = np.mean(group_a) - np.mean(group_b)
        simulated_diffs.append(sim_diff)
    
    # Calculate p-value
    p_value = np.sum(np.array(simulated_diffs) >= observed_diff) / num_permutations
    
    print(f"\nPermutation Test Results:")
    print(f"P-value: {p_value:.4f}")
    print(f"Significance level (α): 0.05")
    
    if p_value < 0.05:
        print("\n✅ CONCLUSION: Reject null hypothesis")
        print("There is statistically significant evidence that OpenAI models have higher math scores than Google models.")
    else:
        print("\n❌ CONCLUSION: Fail to reject null hypothesis")
        print("No statistically significant difference found between OpenAI and Google math scores.")
    
    # Visualization
    plt.figure(figsize=(12, 5))
    
    # Box plot comparison
    plt.subplot(1, 2, 1)
    data_to_plot = [openai_math, google_math]
    plt.boxplot(data_to_plot, labels=['OpenAI', 'Google'])
    plt.title('Math Index Distribution: OpenAI vs Google')
    plt.ylabel('Math Index')
    plt.grid(True, alpha=0.3)
    
    # Permutation test distribution
    plt.subplot(1, 2, 2)
    plt.hist(simulated_diffs, bins=50, alpha=0.7, density=True, label='Null Distribution')
    plt.axvline(observed_diff, color='red', linestyle='--', linewidth=2, label=f'Observed Diff: {observed_diff:.2f}')
    plt.title('Permutation Test Distribution')
    plt.xlabel('Difference in Means')
    plt.ylabel('Density')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
else:
    print("Insufficient data for comparison.")

# Hypothesis 2: Performance Improvement Over Time
print("=== HYPOTHESIS 2: Performance Improvement Over Time ===")

# Create composite performance score
df_clean['composite_score'] = df_clean[['intelligence_index', 'coding_index', 'math_index']].mean(axis=1)

# Filter data by time periods
early_models = df_clean[df_clean['timeline_year'] == 2023]['composite_score'].dropna()
recent_models = df_clean[df_clean['timeline_year'].isin([2024, 2025])]['composite_score'].dropna()

print(f"Models from 2023: {len(early_models)}")
print(f"Models from 2024-2025: {len(recent_models)}")

if len(early_models) > 0 and len(recent_models) > 0:
    # Descriptive statistics
    early_mean = early_models.mean()
    recent_mean = recent_models.mean()
    observed_diff = recent_mean - early_mean
    
    print(f"\nDescriptive Statistics:")
    print(f"2023 Models - Mean: {early_mean:.2f}, Std: {early_models.std():.2f}")
    print(f"2024-2025 Models - Mean: {recent_mean:.2f}, Std: {recent_models.std():.2f}")
    print(f"Observed Difference: {observed_diff:.2f}")
    
    # Welch's t-test (unequal variances)
    t_stat, p_value_ttest = stats.ttest_ind(recent_models, early_models, equal_var=False)
    
    # Effect size (Cohen's d)
    pooled_std = np.sqrt(((len(early_models) - 1) * early_models.var() + 
                         (len(recent_models) - 1) * recent_models.var()) / 
                        (len(early_models) + len(recent_models) - 2))
    cohens_d = observed_diff / pooled_std
    
    print(f"\nWelch's t-test Results:")
    print(f"t-statistic: {t_stat:.4f}")
    print(f"P-value: {p_value_ttest:.4f}")
    print(f"Cohen's d (effect size): {cohens_d:.4f}")
    
    # Interpret effect size
    if abs(cohens_d) < 0.2:
        effect_interpretation = "negligible"
    elif abs(cohens_d) < 0.5:
        effect_interpretation = "small"
    elif abs(cohens_d) < 0.8:
        effect_interpretation = "medium"
    else:
        effect_interpretation = "large"
    
    print(f"Effect size interpretation: {effect_interpretation}")
    
    if p_value_ttest < 0.05:
        print("\n✅ CONCLUSION: Reject null hypothesis")
        print(f"There is statistically significant evidence that models from 2024-2025 perform better than 2023 models (effect size: {effect_interpretation}).")
    else:
        print("\n❌ CONCLUSION: Fail to reject null hypothesis")
        print("No statistically significant improvement found over time.")
    
    # Visualization
    plt.figure(figsize=(12, 5))
    
    # Distribution comparison
    plt.subplot(1, 2, 1)
    plt.hist(early_models, alpha=0.7, label='2023 Models', bins=15)
    plt.hist(recent_models, alpha=0.7, label='2024-2025 Models', bins=15)
    plt.xlabel('Composite Performance Score')
    plt.ylabel('Frequency')
    plt.title('Performance Distribution by Time Period')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Box plot comparison
    plt.subplot(1, 2, 2)
    data_to_plot = [early_models, recent_models]
    plt.boxplot(data_to_plot, labels=['2023', '2024-2025'])
    plt.title('Performance Comparison by Time Period')
    plt.ylabel('Composite Performance Score')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
else:
    print("Insufficient data for temporal comparison.")

# Hypothesis 3: Company Specialization Analysis
print("=== HYPOTHESIS 3: Company Specialization Analysis ===")

# Focus on companies with sufficient data
company_counts = df_clean['creator_name'].value_counts()
major_companies = company_counts[company_counts >= 3].index.tolist()

print(f"Companies with 3+ models: {len(major_companies)}")
print(f"Companies: {major_companies}")

# Calculate company averages for each capability
company_capabilities = df_clean[df_clean['creator_name'].isin(major_companies)].groupby('creator_name').agg({
    'intelligence_index': 'mean',
    'coding_index': 'mean',
    'math_index': 'mean'
}).round(2)

print("\nCompany Average Capabilities:")
print(company_capabilities)

# Perform ANOVA for each capability
capabilities = ['intelligence_index', 'coding_index', 'math_index']
anova_results = {}

for capability in capabilities:
    groups = []
    for company in major_companies:
        company_data = df_clean[df_clean['creator_name'] == company][capability].dropna()
        if len(company_data) > 0:
            groups.append(company_data)
    
    if len(groups) >= 2:
        f_stat, p_value = stats.f_oneway(*groups)
        anova_results[capability] = {'f_stat': f_stat, 'p_value': p_value}

print("\nANOVA Results (Testing for differences between companies):")
for capability, results in anova_results.items():
    print(f"{capability}:")
    print(f"  F-statistic: {results['f_stat']:.4f}")
    print(f"  P-value: {results['p_value']:.4f}")
    if results['p_value'] < 0.05:
        print(f"  ✅ Significant differences found between companies")
    else:
        print(f"  ❌ No significant differences found")

# Calculate specialization index for each company
company_capabilities['max_capability'] = company_capabilities.idxmax(axis=1)
company_capabilities['specialization_score'] = (company_capabilities.max(axis=1) - 
                                               company_capabilities.mean(axis=1))

print("\nCompany Specializations:")
specialization_summary = company_capabilities[['max_capability', 'specialization_score']].sort_values('specialization_score', ascending=False)
print(specialization_summary)

# Visualization
plt.figure(figsize=(15, 10))

# Heatmap of company capabilities
plt.subplot(2, 2, 1)
capability_matrix = company_capabilities[capabilities]
sns.heatmap(capability_matrix, annot=True, cmap='YlOrRd', fmt='.1f')
plt.title('Company Capability Heatmap')
plt.ylabel('Company')

# Specialization scores
plt.subplot(2, 2, 2)
plt.barh(range(len(specialization_summary)), specialization_summary['specialization_score'])
plt.yticks(range(len(specialization_summary)), specialization_summary.index)
plt.xlabel('Specialization Score')
plt.title('Company Specialization Strength')
plt.grid(True, alpha=0.3)

# Capability distribution by company
plt.subplot(2, 1, 2)
df_major = df_clean[df_clean['creator_name'].isin(major_companies)]
df_melted = df_major.melt(id_vars=['creator_name'], 
                         value_vars=capabilities,
                         var_name='capability', 
                         value_name='score')
sns.boxplot(data=df_melted, x='creator_name', y='score', hue='capability')
plt.xticks(rotation=45)
plt.title('Capability Distribution by Company')
plt.ylabel('Performance Score')

plt.tight_layout()
plt.show()

# Statistical conclusion
significant_capabilities = [cap for cap, results in anova_results.items() if results['p_value'] < 0.05]
if len(significant_capabilities) > 0:
    print(f"\n✅ CONCLUSION: Reject null hypothesis for {significant_capabilities}")
    print("Companies show significant specialization patterns in these capabilities.")
else:
    print("\n❌ CONCLUSION: Fail to reject null hypothesis")
    print("No significant specialization patterns found between companies.")

# Hypothesis 4: Price-Performance Correlation
print("=== HYPOTHESIS 4: Price-Performance Correlation ===")

# Filter models with pricing data
price_data = df_clean[df_clean['price_1m_blended'] > 0].copy()
print(f"Models with pricing data: {len(price_data)}")

if len(price_data) > 10:
    # Calculate correlations
    correlations = {}
    for capability in capabilities:
        valid_data = price_data[[capability, 'price_1m_blended']].dropna()
        if len(valid_data) > 5:
            corr_coef, p_value = stats.pearsonr(valid_data['price_1m_blended'], valid_data[capability])
            correlations[capability] = {'correlation': corr_coef, 'p_value': p_value, 'n': len(valid_data)}
    
    print("\nPrice-Performance Correlations:")
    for capability, results in correlations.items():
        print(f"{capability}:")
        print(f"  Correlation coefficient: {results['correlation']:.4f}")
        print(f"  P-value: {results['p_value']:.4f}")
        print(f"  Sample size: {results['n']}")
        if results['p_value'] < 0.05:
            direction = 'positive' if results['correlation'] > 0 else 'negative'
            print(f"  ✅ Significant {direction} correlation found")
        else:
            print(f"  ❌ No significant correlation found")
    
    # Composite score correlation
    price_data['composite_score'] = price_data[capabilities].mean(axis=1)
    composite_corr, composite_p = stats.pearsonr(price_data['price_1m_blended'], price_data['composite_score'])
    
    print(f"\nComposite Score Correlation:")
    print(f"  Correlation coefficient: {composite_corr:.4f}")
    print(f"  P-value: {composite_p:.4f}")
    
    # Visualization
    plt.figure(figsize=(15, 10))
    
    for i, capability in enumerate(capabilities, 1):
        plt.subplot(2, 2, i)
        valid_data = price_data[[capability, 'price_1m_blended']].dropna()
        plt.scatter(valid_data['price_1m_blended'], valid_data[capability], alpha=0.6)
        
        # Add trend line
        z = np.polyfit(valid_data['price_1m_blended'], valid_data[capability], 1)
        p = np.poly1d(z)
        plt.plot(valid_data['price_1m_blended'], p(valid_data['price_1m_blended']), "r--", alpha=0.8)
        
        plt.xlabel('Price (per 1M tokens)')
        plt.ylabel(capability.replace('_', ' ').title())
        plt.title(f'{capability.replace("_", " ").title()} vs Price')
        plt.grid(True, alpha=0.3)
    
    # Composite score plot
    plt.subplot(2, 2, 4)
    plt.scatter(price_data['price_1m_blended'], price_data['composite_score'], alpha=0.6)
    z = np.polyfit(price_data['price_1m_blended'], price_data['composite_score'], 1)
    p = np.poly1d(z)
    plt.plot(price_data['price_1m_blended'], p(price_data['price_1m_blended']), "r--", alpha=0.8)
    plt.xlabel('Price (per 1M tokens)')
    plt.ylabel('Composite Score')
    plt.title('Composite Performance vs Price')
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Statistical conclusion
    significant_correlations = [cap for cap, results in correlations.items() if results['p_value'] < 0.05]
    if len(significant_correlations) > 0 or composite_p < 0.05:
        print(f"\n✅ CONCLUSION: Reject null hypothesis")
        print(f"Significant price-performance correlations found for: {significant_correlations}")
        if composite_p < 0.05:
            print(f"Composite score also shows significant correlation (r={composite_corr:.3f})")
    else:
        print("\n❌ CONCLUSION: Fail to reject null hypothesis")
        print("No significant price-performance correlations found.")
        
else:
    print("Insufficient pricing data for correlation analysis.")

# Hypothesis 5: Capability Interdependence
print("=== HYPOTHESIS 5: Capability Interdependence ===")

# Calculate correlation matrix
capability_data = df_clean[capabilities].dropna()
print(f"Models with complete capability data: {len(capability_data)}")

if len(capability_data) > 10:
    # Correlation matrix
    corr_matrix = capability_data.corr()
    print("\nCapability Correlation Matrix:")
    print(corr_matrix.round(4))
    
    # Test each pair for significance
    capability_pairs = [
        ('intelligence_index', 'coding_index'),
        ('intelligence_index', 'math_index'),
        ('coding_index', 'math_index')
    ]
    
    pair_results = {}
    print("\nPairwise Correlation Tests:")
    for cap1, cap2 in capability_pairs:
        corr_coef, p_value = stats.pearsonr(capability_data[cap1], capability_data[cap2])
        pair_results[f"{cap1}_vs_{cap2}"] = {'correlation': corr_coef, 'p_value': p_value}
        
        print(f"\n{cap1} vs {cap2}:")
        print(f"  Correlation: {corr_coef:.4f}")
        print(f"  P-value: {p_value:.4f}")
        
        # Interpret correlation strength
        abs_corr = abs(corr_coef)
        if abs_corr < 0.3:
            strength = "weak"
        elif abs_corr < 0.7:
            strength = "moderate"
        else:
            strength = "strong"
        
        if p_value < 0.05:
            print(f"  ✅ Significant {strength} positive correlation")
        else:
            print(f"  ❌ No significant correlation")
    
    # Visualization
    plt.figure(figsize=(15, 10))
    
    # Correlation heatmap
    plt.subplot(2, 2, 1)
    sns.heatmap(corr_matrix, annot=True, cmap='coolwarm', center=0, 
                square=True, fmt='.3f', cbar_kws={'shrink': 0.8})
    plt.title('Capability Correlation Heatmap')
    
    # Scatter plots for each pair
    for i, (cap1, cap2) in enumerate(capability_pairs, 2):
        plt.subplot(2, 2, i)
        plt.scatter(capability_data[cap1], capability_data[cap2], alpha=0.6)
        
        # Add trend line
        z = np.polyfit(capability_data[cap1], capability_data[cap2], 1)
        p = np.poly1d(z)
        plt.plot(capability_data[cap1], p(capability_data[cap1]), "r--", alpha=0.8)
        
        plt.xlabel(cap1.replace('_', ' ').title())
        plt.ylabel(cap2.replace('_', ' ').title())
        
        corr_coef = pair_results[f"{cap1}_vs_{cap2}"]['correlation']
        plt.title(f'{cap1.replace("_", " ").title()} vs {cap2.replace("_", " ").title()}\n(r = {corr_coef:.3f})')
        plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Statistical conclusion
    significant_pairs = [pair for pair, results in pair_results.items() if results['p_value'] < 0.05]
    if len(significant_pairs) > 0:
        print(f"\n✅ CONCLUSION: Reject null hypothesis")
        print(f"Significant correlations found between capabilities: {len(significant_pairs)}/3 pairs")
        print("AI capabilities show significant interdependence.")
    else:
        print("\n❌ CONCLUSION: Fail to reject null hypothesis")
        print("No significant correlations found between capabilities.")
        
else:
    print("Insufficient data for capability correlation analysis.")

# Data preparation for predictive modeling
print("=== PREDICTIVE MODELING: Data Preparation ===")

# Create a comprehensive dataset for modeling
modeling_data = df.copy()

# Handle missing timeline data
modeling_data = modeling_data.dropna(subset=['timeline_year', 'timeline_month'])

# Create time-based features
modeling_data['timeline_decimal'] = modeling_data['timeline_year'] + (modeling_data['timeline_month'] - 1) / 12
modeling_data['months_since_2023'] = (modeling_data['timeline_year'] - 2023) * 12 + modeling_data['timeline_month']

# Encode categorical variables
le_company = LabelEncoder()
modeling_data['company_encoded'] = le_company.fit_transform(modeling_data['creator_name'])

# Create company experience feature (number of models released by company up to that point)
modeling_data = modeling_data.sort_values(['creator_name', 'timeline_decimal'])
modeling_data['company_experience'] = modeling_data.groupby('creator_name').cumcount() + 1

print(f"Dataset size for modeling: {len(modeling_data)}")
print(f"Timeline range: {modeling_data['timeline_decimal'].min():.1f} - {modeling_data['timeline_decimal'].max():.1f}")
print(f"Companies: {modeling_data['creator_name'].nunique()}")

# Check data availability for each target variable
target_availability = {
    'intelligence_index': modeling_data['intelligence_index'].notna().sum(),
    'coding_index': modeling_data['coding_index'].notna().sum(),
    'math_index': modeling_data['math_index'].notna().sum()
}

print("\nTarget variable availability:")
for target, count in target_availability.items():
    print(f"  {target}: {count} models ({count/len(modeling_data)*100:.1f}%)")

# Model 1: Intelligence Index Prediction
print("=== MODEL 1: Intelligence Index Prediction ===")

# Prepare data for intelligence prediction
intel_data = modeling_data.dropna(subset=['intelligence_index']).copy()
print(f"Training data size: {len(intel_data)}")

if len(intel_data) > 20:
    # Define features
    feature_columns = [
        'timeline_decimal', 'months_since_2023', 'company_encoded', 'company_experience'
    ]
    
    # Add other performance metrics as features if available
    additional_features = ['coding_index', 'math_index', 'price_1m_blended']
    for feature in additional_features:
        if intel_data[feature].notna().sum() > len(intel_data) * 0.5:  # If >50% data available
            feature_columns.append(feature)
    
    print(f"Features used: {feature_columns}")
    
    # Prepare feature matrix
    X = intel_data[feature_columns].copy()
    
    # Handle missing values in features
    for col in X.columns:
        if X[col].dtype in ['float64', 'int64']:
            X[col] = X[col].fillna(X[col].median())
    
    y = intel_data['intelligence_index']
    
    # Split data chronologically (earlier models for training, later for testing)
    split_time = intel_data['timeline_decimal'].quantile(0.8)
    train_mask = intel_data['timeline_decimal'] <= split_time
    
    X_train, X_test = X[train_mask], X[~train_mask]
    y_train, y_test = y[train_mask], y[~train_mask]
    
    print(f"Training set: {len(X_train)} models (up to {split_time:.1f})")
    print(f"Test set: {len(X_test)} models (after {split_time:.1f})")
    
    if len(X_train) > 10 and len(X_test) > 3:
        # Scale features
        scaler = StandardScaler()
        X_train_scaled = scaler.fit_transform(X_train)
        X_test_scaled = scaler.transform(X_test)
        
        # Train multiple models
        models = {
            'Linear Regression': LinearRegression(),
            'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42)
        }
        
        model_results = {}
        
        for name, model in models.items():
            if name == 'Linear Regression':
                model.fit(X_train_scaled, y_train)
                y_pred = model.predict(X_test_scaled)
                y_train_pred = model.predict(X_train_scaled)
            else:
                model.fit(X_train, y_train)
                y_pred = model.predict(X_test)
                y_train_pred = model.predict(X_train)
            
            # Calculate metrics
            train_r2 = r2_score(y_train, y_train_pred)
            test_r2 = r2_score(y_test, y_pred)
            test_rmse = np.sqrt(mean_squared_error(y_test, y_pred))
            test_mae = mean_absolute_error(y_test, y_pred)
            
            model_results[name] = {
                'train_r2': train_r2,
                'test_r2': test_r2,
                'test_rmse': test_rmse,
                'test_mae': test_mae,
                'predictions': y_pred,
                'model': model
            }
            
            print(f"\n{name} Results:")
            print(f"  Training R²: {train_r2:.4f}")
            print(f"  Test R²: {test_r2:.4f}")
            print(f"  Test RMSE: {test_rmse:.4f}")
            print(f"  Test MAE: {test_mae:.4f}")
        
        # Select best model
        best_model_name = max(model_results.keys(), key=lambda k: model_results[k]['test_r2'])
        best_model = model_results[best_model_name]
        
        print(f"\n🏆 Best Model: {best_model_name} (Test R² = {best_model['test_r2']:.4f})")
        
        # Feature importance (for Random Forest)
        if best_model_name == 'Random Forest':
            feature_importance = pd.DataFrame({
                'feature': feature_columns,
                'importance': best_model['model'].feature_importances_
            }).sort_values('importance', ascending=False)
            
            print("\nFeature Importance:")
            for _, row in feature_importance.iterrows():
                print(f"  {row['feature']}: {row['importance']:.4f}")
        
        # Visualization
        plt.figure(figsize=(15, 10))
        
        # Actual vs Predicted
        plt.subplot(2, 2, 1)
        plt.scatter(y_test, best_model['predictions'], alpha=0.7)
        plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'r--', lw=2)
        plt.xlabel('Actual Intelligence Index')
        plt.ylabel('Predicted Intelligence Index')
        plt.title(f'Actual vs Predicted ({best_model_name})')
        plt.grid(True, alpha=0.3)
        
        # Residuals plot
        plt.subplot(2, 2, 2)
        residuals = y_test - best_model['predictions']
        plt.scatter(best_model['predictions'], residuals, alpha=0.7)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('Predicted Intelligence Index')
        plt.ylabel('Residuals')
        plt.title('Residuals Plot')
        plt.grid(True, alpha=0.3)
        
        # Timeline trend
        plt.subplot(2, 2, 3)
        test_data = intel_data[~train_mask]
        plt.scatter(test_data['timeline_decimal'], y_test, label='Actual', alpha=0.7)
        plt.scatter(test_data['timeline_decimal'], best_model['predictions'], label='Predicted', alpha=0.7)
        plt.xlabel('Timeline (Year)')
        plt.ylabel('Intelligence Index')
        plt.title('Timeline Predictions')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # Feature importance (if Random Forest)
        if best_model_name == 'Random Forest':
            plt.subplot(2, 2, 4)
            plt.barh(range(len(feature_importance)), feature_importance['importance'])
            plt.yticks(range(len(feature_importance)), feature_importance['feature'])
            plt.xlabel('Feature Importance')
            plt.title('Feature Importance (Random Forest)')
            plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
    else:
        print("Insufficient data for train/test split.")
        
else:
    print("Insufficient data for intelligence prediction modeling.")

# Model 2: Company Leadership Prediction
print("=== MODEL 2: Company Leadership Prediction ===")

# Analyze company performance trends over time
company_trends = []

for company in modeling_data['creator_name'].unique():
    company_data = modeling_data[modeling_data['creator_name'] == company].copy()
    
    if len(company_data) >= 3:  # Need at least 3 models for trend analysis
        company_data = company_data.sort_values('timeline_decimal')
        
        for capability in capabilities:
            cap_data = company_data.dropna(subset=[capability])
            
            if len(cap_data) >= 3:
                # Calculate trend (slope of performance over time)
                x = cap_data['timeline_decimal'].values
                y = cap_data[capability].values
                
                if len(x) > 1 and np.std(x) > 0:
                    slope, intercept, r_value, p_value, std_err = stats.linregress(x, y)
                    
                    # Recent performance (average of last 2 models)
                    recent_performance = cap_data[capability].tail(2).mean()
                    
                    # Overall performance
                    avg_performance = cap_data[capability].mean()
                    
                    company_trends.append({
                        'company': company,
                        'capability': capability,
                        'trend_slope': slope,
                        'trend_r2': r_value**2,
                        'trend_p_value': p_value,
                        'recent_performance': recent_performance,
                        'avg_performance': avg_performance,
                        'model_count': len(cap_data),
                        'latest_year': cap_data['timeline_decimal'].max()
                    })

trends_df = pd.DataFrame(company_trends)

if len(trends_df) > 0:
    print(f"Company-capability combinations analyzed: {len(trends_df)}")
    
    # Predict future leaders for each capability
    future_predictions = {}
    
    for capability in capabilities:
        cap_trends = trends_df[trends_df['capability'] == capability].copy()
        
        if len(cap_trends) > 0:
            # Create composite leadership score
            # Factors: recent performance (40%), trend slope (30%), model count (20%), trend significance (10%)
            cap_trends['leadership_score'] = (
                0.4 * (cap_trends['recent_performance'] / cap_trends['recent_performance'].max()) +
                0.3 * (cap_trends['trend_slope'] / cap_trends['trend_slope'].max()) +
                0.2 * (cap_trends['model_count'] / cap_trends['model_count'].max()) +
                0.1 * (1 - cap_trends['trend_p_value'])  # Lower p-value = higher score
            )
            
            # Predict performance in 2026 (extrapolate trends)
            future_year = 2026.0
            cap_trends['predicted_2026'] = (
                cap_trends['recent_performance'] + 
                cap_trends['trend_slope'] * (future_year - cap_trends['latest_year'])
            )
            
            # Rank companies
            cap_trends = cap_trends.sort_values('leadership_score', ascending=False)
            future_predictions[capability] = cap_trends
            
            print(f"\n{capability.replace('_', ' ').title()} - Top 5 Future Leaders:")
            top_5 = cap_trends.head(5)
            for i, (_, row) in enumerate(top_5.iterrows(), 1):
                print(f"  {i}. {row['company']} (Score: {row['leadership_score']:.3f}, "
                      f"Trend: {row['trend_slope']:+.2f}/year, "
                      f"Recent: {row['recent_performance']:.1f})")
    
    # Visualization
    plt.figure(figsize=(18, 12))
    
    for i, capability in enumerate(capabilities, 1):
        if capability in future_predictions:
            cap_data = future_predictions[capability]
            
            # Leadership scores
            plt.subplot(3, 3, i)
            top_10 = cap_data.head(10)
            plt.barh(range(len(top_10)), top_10['leadership_score'])
            plt.yticks(range(len(top_10)), top_10['company'])
            plt.xlabel('Leadership Score')
            plt.title(f'{capability.replace("_", " ").title()} - Leadership Scores')
            plt.grid(True, alpha=0.3)
            
            # Performance trends
            plt.subplot(3, 3, i + 3)
            plt.scatter(cap_data['recent_performance'], cap_data['trend_slope'], 
                       s=cap_data['model_count']*20, alpha=0.6)
            
            # Annotate top companies
            for _, row in cap_data.head(5).iterrows():
                plt.annotate(row['company'], 
                           (row['recent_performance'], row['trend_slope']),
                           xytext=(5, 5), textcoords='offset points', fontsize=8)
            
            plt.xlabel('Recent Performance')
            plt.ylabel('Trend Slope (per year)')
            plt.title(f'{capability.replace("_", " ").title()} - Performance vs Trend')
            plt.grid(True, alpha=0.3)
            
            # 2026 Predictions
            plt.subplot(3, 3, i + 6)
            top_10_future = cap_data.nlargest(10, 'predicted_2026')
            plt.barh(range(len(top_10_future)), top_10_future['predicted_2026'])
            plt.yticks(range(len(top_10_future)), top_10_future['company'])
            plt.xlabel('Predicted 2026 Performance')
            plt.title(f'{capability.replace("_", " ").title()} - 2026 Predictions')
            plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Summary insights
    print("\n=== COMPANY LEADERSHIP INSIGHTS ===")
    
    # Overall leaders across all capabilities
    overall_leadership = trends_df.groupby('company').agg({
        'leadership_score': 'mean',
        'trend_slope': 'mean',
        'recent_performance': 'mean',
        'model_count': 'sum'
    }).sort_values('leadership_score', ascending=False)
    
    print("\nTop 5 Overall AI Leaders (across all capabilities):")
    for i, (company, row) in enumerate(overall_leadership.head(5).iterrows(), 1):
        print(f"  {i}. {company} (Avg Score: {row['leadership_score']:.3f}, "
              f"Avg Trend: {row['trend_slope']:+.2f}/year)")
    
    # Companies with strongest positive trends
    strongest_trends = trends_df[trends_df['trend_p_value'] < 0.1].nlargest(5, 'trend_slope')
    print("\nCompanies with Strongest Improvement Trends:")
    for i, (_, row) in enumerate(strongest_trends.iterrows(), 1):
        print(f"  {i}. {row['company']} in {row['capability']} "
              f"(+{row['trend_slope']:.2f}/year, R²={row['trend_r2']:.3f})")
    
else:
    print("Insufficient data for company leadership analysis.")

# Model 3: Timeline Trend Prediction
print("=== MODEL 3: Timeline Trend Prediction ===")

# Aggregate data by time periods
timeline_analysis = modeling_data.copy()
timeline_analysis['quarter'] = timeline_analysis['timeline_year'] + (timeline_analysis['timeline_month'] - 1) // 3 * 0.25

# Quarterly aggregations
quarterly_stats = timeline_analysis.groupby('quarter').agg({
    'model_name': 'count',  # Number of models released
    'intelligence_index': ['mean', 'max', 'std'],
    'coding_index': ['mean', 'max', 'std'],
    'math_index': ['mean', 'max', 'std'],
    'creator_name': 'nunique'  # Number of unique companies
}).round(3)

# Flatten column names
quarterly_stats.columns = ['_'.join(col).strip() for col in quarterly_stats.columns]
quarterly_stats = quarterly_stats.reset_index()

print(f"Quarterly data points: {len(quarterly_stats)}")
print(f"Time range: Q{int((quarterly_stats['quarter'].min() % 1) * 4) + 1} {int(quarterly_stats['quarter'].min())} - "
      f"Q{int((quarterly_stats['quarter'].max() % 1) * 4) + 1} {int(quarterly_stats['quarter'].max())}")

if len(quarterly_stats) >= 4:
    # Predict future trends using linear regression
    future_quarters = np.arange(quarterly_stats['quarter'].max() + 0.25, 
                               quarterly_stats['quarter'].max() + 2.25, 0.25)
    
    trend_predictions = {}
    
    # Metrics to predict
    prediction_metrics = [
        'model_name_count',  # Release frequency
        'intelligence_index_mean', 'intelligence_index_max',
        'coding_index_mean', 'coding_index_max',
        'math_index_mean', 'math_index_max',
        'creator_name_nunique'  # Market diversity
    ]
    
    for metric in prediction_metrics:
        if metric in quarterly_stats.columns:
            # Remove NaN values
            valid_data = quarterly_stats[['quarter', metric]].dropna()
            
            if len(valid_data) >= 3:
                X = valid_data['quarter'].values.reshape(-1, 1)
                y = valid_data[metric].values
                
                # Fit linear regression
                model = LinearRegression()
                model.fit(X, y)
                
                # Predict future values
                future_predictions = model.predict(future_quarters.reshape(-1, 1))
                
                # Calculate trend statistics
                r2 = model.score(X, y)
                slope = model.coef_[0]
                
                trend_predictions[metric] = {
                    'slope': slope,
                    'r2': r2,
                    'current_value': y[-1],
                    'predicted_2026': future_predictions[-1],
                    'predictions': future_predictions
                }
    
    # Display trend analysis
    print("\nTrend Analysis Results:")
    for metric, results in trend_predictions.items():
        metric_name = metric.replace('_', ' ').title()
        print(f"\n{metric_name}:")
        print(f"  Current Value: {results['current_value']:.2f}")
        print(f"  Predicted 2026: {results['predicted_2026']:.2f}")
        print(f"  Trend Slope: {results['slope']:+.3f} per quarter")
        print(f"  R² Score: {results['r2']:.3f}")
        
        # Interpret trend
        if abs(results['slope']) < 0.01:
            trend_desc = "stable"
        elif results['slope'] > 0:
            trend_desc = "increasing"
        else:
            trend_desc = "decreasing"
        
        confidence = "high" if results['r2'] > 0.7 else "moderate" if results['r2'] > 0.4 else "low"
        print(f"  Trend: {trend_desc} ({confidence} confidence)")
    
    # Visualization
    plt.figure(figsize=(20, 15))
    
    # Key metrics over time
    key_metrics = [
        ('model_name_count', 'Models Released per Quarter'),
        ('intelligence_index_mean', 'Average Intelligence Index'),
        ('intelligence_index_max', 'Maximum Intelligence Index'),
        ('creator_name_nunique', 'Number of Active Companies')
    ]
    
    for i, (metric, title) in enumerate(key_metrics, 1):
        if metric in trend_predictions:
            plt.subplot(3, 3, i)
            
            # Plot historical data
            valid_data = quarterly_stats[['quarter', metric]].dropna()
            plt.plot(valid_data['quarter'], valid_data[metric], 'bo-', label='Historical', markersize=6)
            
            # Plot trend line
            all_quarters = np.concatenate([valid_data['quarter'].values, future_quarters])
            model = LinearRegression()
            model.fit(valid_data['quarter'].values.reshape(-1, 1), valid_data[metric].values)
            trend_line = model.predict(all_quarters.reshape(-1, 1))
            plt.plot(all_quarters, trend_line, 'r--', alpha=0.7, label='Trend')
            
            # Plot predictions
            plt.plot(future_quarters, trend_predictions[metric]['predictions'], 
                    'go-', label='Predicted', markersize=6)
            
            plt.xlabel('Year')
            plt.ylabel(title)
            plt.title(f'{title} Over Time')
            plt.legend()
            plt.grid(True, alpha=0.3)
    
    # Performance evolution comparison
    plt.subplot(3, 3, 5)
    for capability in ['intelligence_index_mean', 'coding_index_mean', 'math_index_mean']:
        if capability in quarterly_stats.columns:
            valid_data = quarterly_stats[['quarter', capability]].dropna()
            plt.plot(valid_data['quarter'], valid_data[capability], 
                    'o-', label=capability.replace('_mean', '').replace('_', ' ').title(), markersize=4)
    
    plt.xlabel('Year')
    plt.ylabel('Average Performance Score')
    plt.title('Capability Evolution Comparison')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Market concentration over time
    plt.subplot(3, 3, 6)
    if 'creator_name_nunique' in quarterly_stats.columns and 'model_name_count' in quarterly_stats.columns:
        # Calculate models per company (concentration metric)
        quarterly_stats['models_per_company'] = (quarterly_stats['model_name_count'] / 
                                                quarterly_stats['creator_name_nunique'])
        
        valid_data = quarterly_stats[['quarter', 'models_per_company']].dropna()
        plt.plot(valid_data['quarter'], valid_data['models_per_company'], 'mo-', markersize=6)
        
        plt.xlabel('Year')
        plt.ylabel('Models per Company')
        plt.title('Market Concentration Trend')
        plt.grid(True, alpha=0.3)
    
    # Performance variance over time
    plt.subplot(3, 3, 7)
    for capability in ['intelligence_index_std', 'coding_index_std', 'math_index_std']:
        if capability in quarterly_stats.columns:
            valid_data = quarterly_stats[['quarter', capability]].dropna()
            plt.plot(valid_data['quarter'], valid_data[capability], 
                    's-', label=capability.replace('_std', '').replace('_', ' ').title(), markersize=4)
    
    plt.xlabel('Year')
    plt.ylabel('Performance Standard Deviation')
    plt.title('Performance Variance Over Time')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.show()
    
    # Future predictions summary
    print("\n=== 2026 PREDICTIONS SUMMARY ===")
    
    if 'model_name_count' in trend_predictions:
        pred = trend_predictions['model_name_count']
        print(f"Expected quarterly model releases in 2026: {pred['predicted_2026']:.0f} "
              f"(vs {pred['current_value']:.0f} currently)")
    
    if 'intelligence_index_max' in trend_predictions:
        pred = trend_predictions['intelligence_index_max']
        print(f"Expected peak intelligence score in 2026: {pred['predicted_2026']:.1f} "
              f"(vs {pred['current_value']:.1f} currently)")
    
    if 'creator_name_nunique' in trend_predictions:
        pred = trend_predictions['creator_name_nunique']
        print(f"Expected active companies in 2026: {pred['predicted_2026']:.0f} "
              f"(vs {pred['current_value']:.0f} currently)")
    
else:
    print("Insufficient quarterly data for timeline trend analysis.")

















fig, axes = plt.subplots(1, 3, figsize=(15, 5))

axes[0].bar(top10_math['model_name'], top10_math['math_index'])
axes[0].set_title('Top 10 by Math Index')
axes[1].bar(top10_coding['model_name'], top10_coding['coding_index'])
axes[1].set_title('Top 10 by Coding Index')
axes[2].bar(top10_intel['model_name'], top10_intel['intelligence_index'])
axes[2].set_title('Top 10 by Intelligence Index')

for ax in axes:
    ax.set_xticklabels(ax.get_xticklabels(), rotation=45, ha='right')

plt.tight_layout()
plt.show()



import numpy as np
from sklearn.linear_model import LinearRegression
df2 = df.dropna(subset=['math_index', 'coding_index']) 
X = df2[['math_index']].values.reshape(-1, 1)
y = df2['coding_index'].values

# 3. 拟合线性回归模型
model = LinearRegression()
model.fit(X, y)
slope = model.coef_[0]
intercept = model.intercept_
r_sq = model.score(X, y)

# 4. 输出回归结果
print(f"Regression equation: coding_index = {slope:.3f} * math_index + {intercept:.3f}")
print(f"R² (coefficient of determination): {r_sq:.3f}")


# 5. 可视化：散点 + 回归直线
plt.figure(figsize=(6, 6))
plt.scatter(df2['math_index'], df2['coding_index'], alpha=0.6, label='samples')
# 回归直线
x_line = np.linspace(df2['math_index'].min(), df2['math_index'].max(), 100)
y_line = slope * x_line + intercept
plt.plot(x_line, y_line, color='red', linewidth=2, label='linear regression')

plt.xlabel('Math Index')
plt.ylabel('Coding Index')
plt.title('Math vs. Coding Index ')
plt.legend()
plt.tight_layout()
plt.show()


free_models = df[df['price_1m_blended'] == 0][
    ['model_name', 'creator_name', 'price_1m_blended']
].reset_index(drop=True)

# 4. 打印结果
print("Free AI Models (Blended Cost per 1M Tokens = 0):")
print(free_models.to_string(index=False))

free_df = df[df['price_1m_blended'] == 0].copy()

# 3. 找出免费模型中各项能力最优的模型
best_intel = free_df.loc[free_df['intelligence_index'].idxmax(), ['model_name', 'creator_name', 'intelligence_index']]
best_coding = free_df.loc[free_df['coding_index'].idxmax(), ['model_name', 'creator_name', 'coding_index']]
best_math = free_df.loc[free_df['math_index'].idxmax(), ['model_name', 'creator_name', 'math_index']]

# 4. 计算综合能力（平均）并找出前三名
free_df['composite_score'] = free_df[['intelligence_index', 'coding_index', 'math_index']].mean(axis=1)
top3_composite = free_df.nlargest(3, 'composite_score')[
    ['model_name', 'creator_name', 'composite_score']
].reset_index(drop=True)

# 5. 打印结果
print("=== Best Free Models by Individual Ability ===")
print("Intelligence:", best_intel.to_dict())
print("Coding:", best_coding.to_dict())
print("Math:", best_math.to_dict(), "\n")

print("=== Top 3 Free Models by Composite Score ===")
print(top3_composite.to_string(index=False))

perf_cols = ['intelligence_index', 'coding_index', 'math_index']
df['performance_score'] = df[perf_cols].mean(axis=1)
paid_df = df[df['price_1m_blended'] > 0].dropna(subset=['performance_score', 'price_1m_blended']).copy()

# 4. 计算 Pearson 相关系数
corr = paid_df['performance_score'].corr(paid_df['price_1m_blended'])
print(f"Pearson correlation between Performance Score and Blended Cost: {corr:.3f}")

# 5. 线性回归：成本 → 性能
X = paid_df[['price_1m_blended']].values.reshape(-1, 1)
y = paid_df['performance_score'].values
model = LinearRegression().fit(X, y)
slope, intercept = model.coef_[0], model.intercept_
r_sq = model.score(X, y)
print(f"Regression: performance_score = {slope:.3f} * cost + {intercept:.3f}, R² = {r_sq:.3f}")

# 6. 可视化散点与回归线
plt.figure(figsize=(6, 6))
plt.scatter(paid_df['price_1m_blended'], paid_df['performance_score'], alpha=0.6, label='Paid models')
x_line = np.linspace(paid_df['price_1m_blended'].min(), paid_df['price_1m_blended'].max(), 100)
y_line = slope * x_line + intercept
plt.plot(x_line, y_line, color='red', linewidth=2, label='Fit line')

plt.xscale('log')
plt.xlabel('Blended Cost per 1M Tokens (USD, log scale)')
plt.ylabel('Composite Performance Score')
plt.title('Performance vs. Cost (Paid Models Only)')
plt.legend()
plt.grid(True, ls='--', alpha=0.5)
plt.tight_layout()
plt.show()


df['perf_score'] = df[['intelligence_index','coding_index','math_index']].mean(axis=1)

# 2) 排除免费、Cost 为 0 的行
paid = df[df['price_1m_blended']>0].copy()

# 3) 计算性价比
paid['cpr'] = paid['perf_score'] / paid['price_1m_blended']

# 4) 列出性价比 Top5
print(paid.nlargest(5, 'cpr')[['model_name','cpr']])


# 2. 定义主流关键词
# 2. 定义主流关键词
df['cpr'] = df['performance_score'] / df['price_1m_blended']
keywords = ['GPT-4','GPT-3.5','Claude','Gemini','Llama','Mistral','PaLM']
mask = df['model_name'].str.contains('|'.join(keywords), case=False, na=False)
mainstream = df[mask & df['price_1m_blended']>0].copy()

# 3. 取性价比 Top 10 的主流模型
top_mainstream = mainstream.nlargest(10, 'cpr')[['model_name','cpr']]

# 4. 绘制更清晰的柱状图
plt.figure(figsize=(8, 6))
plt.barh(top_mainstream['model_name'], top_mainstream['cpr'], color='skyblue')
plt.xlabel('Cost-Performance Ratio')
plt.title('Top 10 Mainstream LLMs by Cost-Performance')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()

# 显示表格
print(top_mainstream.to_string(index=False))


# 3. 筛选 GPT 系列模型，且性价比为有限值
gpt_df = df[df['model_name'].str.contains("GPT", case=False, na=False)].copy()
gpt_df = gpt_df[np.isfinite(gpt_df['cpr'])]

# 4. 概览 GPT 模型表现
gpt_summary = gpt_df[['model_name', 'intelligence_index', 'coding_index', 'math_index',
                      'price_1m_blended', 'performance_score', 'cpr']]
print("GPT Series Model Performance:\n")
print(gpt_summary.to_string(index=False))

# 5. 可视化：GPT 系列性价比
plt.figure(figsize=(8, 4))
plt.barh(gpt_summary['model_name'], gpt_summary['cpr'], color='orange')
plt.xlabel('Cost-Performance Ratio')
plt.title('GPT Series Models: Cost-Performance Ratio')
plt.gca().invert_yaxis()
plt.tight_layout()
plt.show()

df = df.dropna(subset=['latency_seconds','time_to_first_token_seconds','time_to_first_answer_token'])

# 3. 定义一个函数来展示 TopN 并画图
def show_top_latency(col, topn=10):
    top = df.nlargest(topn, col)[['model_name', col]].reset_index(drop=True)
    print(f"\n=== Top {topn} by {col} ===")
    print(top.to_string(index=False))
    # 可视化
    plt.figure(figsize=(6, topn*0.5))
    plt.barh(top['model_name'], top[col], color='salmon')
    plt.xlabel(col)
    plt.title(f"Top {topn} Models by {col}")
    plt.gca().invert_yaxis()
    plt.tight_layout()
    plt.show()

# 4. 分别调用
show_top_latency('latency_seconds')
show_top_latency('time_to_first_token_seconds')
show_top_latency('time_to_first_answer_token')



plt.figure(figsize=(8, 4))
top10 = avg_intel.head(10)
plt.bar(top10['creator_name'], top10['intelligence_index'], color='skyblue')
plt.title('Top 10 Creators by Avg Intelligence Index')
plt.ylabel('Avg Intelligence Index')
plt.xticks(rotation=45, ha='right')
plt.tight_layout()
plt.show()


plt.figure(figsize=(6, 6))
plt.scatter(df['coding_index'], df['output_speed_tokens_per_sec'], alpha=0.6)
plt.title('Coding Index vs. Output Speed (tokens/s)')
plt.xlabel('Coding Index')
plt.ylabel('Output Speed (tokens/s)')
plt.tight_layout()
plt.show()










